export interface SuperAdminType {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  phoneNumber: string;
  lastLoginAt: string;
  createdAt: string;
  updatedAt: string;
  permissions: PermissionType[];
}

export interface PermissionType {
  id: string;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ClientType {
  contract: String | null | File;
  logo: String | null | File;
  legalRepresentativeSignature: String | null | File;
  businessLicense: String | null | File;
  regulatoryCertificate: String | null | File;
  id: string;
  superAdminId: string;
  companyName: string;
  legalName: string;
  companyRegistrationNumber: string;
  taxIdentificationNumber: string;
  businessType: string;
  websiteUrl: string;
  shortDescription: string;
  logoUrl?: string;
  primaryContactName: string;
  primaryContactDesignation: string;
  primaryContactEmail: string;
  primaryContactPhone: string;
  alternativeContactName: string;
  alternativeContactEmail: string;
  alternativeContactPhone: string;
  headOfficeAddress: string;
  city: string;
  stateProvince: string;
  country: string;
  postalCode: string;
  branchLocation: string;
  billingCycle: string;
  invoiceEmail: string;
  contractUrl?: string;
  legalRepresentativeName: string;
  legalRepresentativeSignatureUrl?: string;
  serviceType: string;
  operatingCities: string;
  fleetSize: number;
  availableVehicleTypes: string;
  wheelchairService: boolean;
  childSeatService: boolean;
  businessLicenseUrl?: string;
  insuranceCompany: string;
  insurancePolicyNumber: string;
  insuranceExpiryDate: string;
  regulatoryCertificatesUrl?: string;
  permitExpiryDate: string;
  tenantId: any;
  status: string;
  createdAt: string;
  updatedAt: string;
  databaseName?: string;
  databaseHost: any;
  databasePort: any;
  databaseUsername: any;
  databasePassword: any;
  databaseCreated: boolean;
  tenant: any;
  permissions: PermissionType[];
  superAdmin: Omit<SuperAdminType, 'PermissionType'>;
}

export interface ClientAdminType {
  id: string;
  email: string;
  isActive: boolean;
  firstName: string;
  lastName: string;
  role: string;
  clientId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  permissions: PermissionType[];
}

export interface DriverDetailsType {
  id: number;
  image?: any;
  driverType: string;
  fullName: string;
  status: boolean;
  driverStatus: string;
  email: string;
  countryCode: string;
  contactNo: string;
  shift: string;
  rating: string;
  region: string;
  gender: string;
  dob: string;
  nationalIdSsn: string;
  rides: number;
  driverVehicleAvailability: string;
  address: string;
  city?: string;
  state?: string;
  zipCode?: string;
  lastActive?: string;
  driverInsuranceRenewalReminder: boolean;
  drivingLicence: string;
  drivingLicenceVerification: boolean;
  workPermit: string;
  workPermitVerification: boolean;
  verification: boolean;
  earnings: number;
  emergencyContactPerson?: string;
  emergencyContact?: string;
  emergencyContactRelation?: string;
  registrationStatus: boolean;
  trips: number;
  tripsToday: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  addedBy?: string;
  deletedBy?: string;
  isDeleted: boolean;
  model: string;
  plateNumber: string;
  vehicleType: string;
  vehicleAvailability: string;
  insurance: string;
  insuranceExpiryDate: string;
  insuranceRenewalReminder: boolean;
  insuranceVerification: boolean;
  vehicleRegistrationVerification: boolean;
  vehicleTrips: number;
  vehicleEarnings: number;
  vehicleRegistration: string;
  color?: any;
}

export type DriverResponseType = {
  success: boolean;
  data: DriverDetailsType[];
  meta: unknown;
};

export interface PassengersDetailsTypeResponse {
  id: string;
  fullName: string;
  emailId: string;
  contactNumber: string;
  gender: string;
  dateOfBirth: string;
  currentAddress: string;
  preferredPaymentMethod: string;
  emergencyContactName: string;
  emergencyContactNumber: string;
  status: string;
  password: string;
  clientId: string;
  token: any;
  createdAt: string;
  updatedAt: string;
}
