import { backendApiClient } from '@/utils/apiClient';
export async function getTablePassengerDetails() {
  const rawResponse = await backendApiClient
    .get('passengers')
    .json()
    .then(response => response);
  return rawResponse.data;
}

export async function addTablePassengersDetails(payload: any) {
  const rawResponse = await backendApiClient
    .post('passengers', { json: payload })
    .json();
  return rawResponse;
}
export async function getSpecificPassengersDetails(id: string) {
  const rawResponse: any = await backendApiClient
    .get(`passengers/${id}`)
    .json()
    .then(response => response);
  return rawResponse;
}

export const deletePassengers = (payload: string) =>
  backendApiClient.delete(`passengers/${payload}`).json();

export const disableDriverQuery = (payload: any) =>
  backendApiClient
    .patch(`passengers/${payload?.id}/status`, {
      json: { status: payload?.status },
    })
    .json();

// export const downloadReport = (payload: string) =>
//   backendApiClient
//     .get(`driver-management/driver/export-driver/csv/${payload}`)
//     .blob();

export const editDriverDetails = (payload: FormData, id: string) =>
  backendApiClient
    .put(`passengers/${id}`, {
      body: payload,
    })
    .json();
